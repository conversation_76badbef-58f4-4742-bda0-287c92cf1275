<!--
 * @Author: 张文轩
 * @Date: 2024-09-25 17:04:48
 * @LastEditTime: 2024-12-04 14:07:47
 * @LastEditors: 张文轩
 * @Description:
 * @FilePath: \ltc-web-ui\src\views\data\SpecialApply\SpecialApplyDetail.vue
 * 记得注释
-->
<template>
  <detail-page :showTitle="false" :showFilter="false">
    <template #title>
      <div>特价申请详情</div>
    </template>
    <template #actions>
      <el-button :type="getActionType(actions[0])" v-if="actions[0]" @click="doAction(actions[0])">
        {{ actions[0].name }}
      </el-button>
      <el-button :type="getActionType(actions[1])" v-if="actions[1]" @click="doAction(actions[1])">
        {{ actions[1].name }}
      </el-button>
      <el-button :type="getActionType(actions[2])" v-if="actions[2]" @click="doAction(actions[2])">
        {{ actions[2].name }}
      </el-button>
      <el-dropdown v-if="actions.length > 3" @command="doAction">
        <el-button size="mini" style="margin: 0px 12px">
          更多<i class="el-icon-arrow-down el-icon--right" />
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item v-for="item in actions.slice(3)" :command="item" :key="item.name">
            {{ item.name }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-button v-if="entity.state !== 'draft'" @click="doApprovalInformation">审批信息</el-button>
      <el-button @click="doBackList">返回列表</el-button>
    </template>
    <template #main>
      <div class="detail">
        <div class="detail-title">
          <StatePopover :state="entity.state | stateFilter" :showIcon="false" />
          <div class="detail-title-num">{{ entity.id }}</div>
          <i v-if="entity.urgentApprove" class="iconfont icon-hang1_icon" />
        </div>
        <div class="detail-tip" v-if="tipInfo">
          <i class="iconfont icon-ic_warningfill" />
          <div class="detail-tip-text">{{ tipInfo }}</div>
        </div>
        <div class="detail-main">
          <!-- <div style="margin-bottom: 8px;">
            <span style="margin-right: 8px;">申请的特价是否包含以下费用项:</span>
            <span>{{ entity.otherExpenses | booleanFilter }}</span>
          </div>
          <div style="margin-bottom: 8px;">
            <span
              >物流费用、延保费、客户佣金、赠送配件金额、额外费用、远期贴现费、信保费、国内运输保险费、国际运输保险费</span
            >
          </div> -->
          <Panel header="基本信息">
            <el-row :gutter="24">
              <el-col :span="12" class="bottom-margin">
                <view-item label="申请主题 :">
                  {{ entity.topic }}
                </view-item>
              </el-col>
              <el-col :span="6" class="bottom-margin">
                <view-item label="特价类型 :">
                  {{
                    entity.specialType == 'importantProjects'
                      ? '重点项目/客户特价'
                      : entity.specialType == 'sampleMachine'
                      ? '样机测试/推广'
                      : '区域指导价调整'
                  }}
                </view-item>
              </el-col>
              <el-col :span="6" class="bottom-margin">
                <view-item label="出口类型 :">
                  {{ entity.exportTypeId | exportTypeFormatter }}
                </view-item>
              </el-col>
              <el-col :span="6" class="bottom-margin">
                <view-item label="子公司 :">
                  {{ getSbsdyNames(entity.sbsdyNames) }}
                </view-item>
              </el-col>
              <el-col
                v-if="entity.exportTypeId !== 'supply'"
                :span="entity.exportTypeId !== ExportType.supply ? 6 : 12"
                class="bottom-margin"
              >
                <view-item label="销售模式 :">
                  {{ entity.saleMode | saleModeFormatter }}
                </view-item>
              </el-col>
              <!-- 供货不显示指导价贸易术语 -->
              <el-col
                v-if="entity.exportTypeId !== ExportType.supply"
                :span="6"
                class="bottom-margin"
              >
                <view-item label="指导价贸易术语 :">
                  {{ entity.incotermsId }}
                </view-item>
              </el-col>
              <el-col :span="6" class="bottom-margin">
                <view-item>
                  <template #label>
                    <span>特价有效期 :</span>
                    <el-tooltip
                      content="申请特价的期望有效期，特价的实际有效期以审批通过后的价格有效期为准"
                      placement="top-start"
                    >
                      <i class="el-icon-info icon-info"></i>
                    </el-tooltip>
                  </template>
                  <div>{{ entity.beginDate }}～{{ entity.endDate }}</div>
                </view-item>
              </el-col>
              <el-col v-if="entity.state == 'approved'" :span="6" class="bottom-margin">
                <view-item label="允许特价产生折扣 :">
                  <div>{{ entity.discount ? '是' : '否' }}</div>
                </view-item>
              </el-col>
            </el-row>
          </Panel>
          <panel header="申请信息">
            <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
              <el-tab-pane label="申请说明" name="application-instructions">
                <SpecialApplyDescriptionDrawerView
                  v-if="activeName === 'application-instructions' && entity.specialType"
                  :id="id"
                  :base-entity="entity"
                />
              </el-tab-pane>
              <el-tab-pane v-if="isHasviewProfit" label="毛利分析" name="profitability-analysis">
                <div class="thrid-tab">
                  <el-table
                    v-loading="grossProfitAnalysisLoading"
                    :data="tableDataSAP"
                    :span-method="spanMethodSAP"
                    max-height="500px"
                  >
                    <el-table-column label="整机物料" minWidth="180" fixed="left">
                      <template #default="scope">
                        <div class="table-cell">
                          <div>物料号：{{ scope.row.specialApplyMat.matCd }}</div>
                          <div>
                            国际产品线：{{
                              (scope.row.specialApplyMat &&
                                scope.row.specialApplyMat.i18ProdGroupName) | text
                            }}
                          </div>
                          <div>
                            机型：{{
                              (scope.row.specialApplyMat && scope.row.specialApplyMat.prodMdlCode)
                                | text
                            }}
                          </div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="配置说明（营销）"
                      minWidth="200"
                      prop="matDesc"
                      show-overflow-tooltip
                    >
                      <template #default="scope">
                        <el-tooltip
                          :disabled="
                            ObjectUtil.isNullOrBlank(scope.row.specialApplyMat) ||
                              ObjectUtil.isNullOrBlank(scope.row.specialApplyMat.matDesc)
                          "
                          popper-class="popper-class"
                          class="item"
                          effect="dark"
                          :content="
                            ObjectUtil.replaceStr(scope.row.specialApplyMat.matDesc, '@!', '/')
                          "
                          placement="top"
                          :open-delay="1000"
                        >
                          <div class="textLength">
                            <template>
                              {{
                                ObjectUtil.replaceStr(scope.row.specialApplyMat.matDesc, '@!', '/')
                                  | text
                              }}
                            </template>
                          </div>
                        </el-tooltip>
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="配置说明（营销）_英文"
                      minWidth="200"
                      prop="matDescEn"
                      show-overflow-tooltip
                    >
                      <template #default="scope">
                        <el-tooltip
                          :disabled="
                            ObjectUtil.isNullOrBlank(scope.row.specialApplyMat) ||
                              ObjectUtil.isNullOrBlank(scope.row.specialApplyMat.matDescEn)
                          "
                          popper-class="popper-class"
                          class="item"
                          effect="dark"
                          :content="
                            ObjectUtil.replaceStr(scope.row.specialApplyMat.matDescEn, '@!', '/')
                          "
                          placement="top"
                          :open-delay="1000"
                        >
                          <div class="textLength">
                            <template>
                              {{
                                ObjectUtil.replaceStr(
                                  scope.row.specialApplyMat.matDescEn,
                                  '@!',
                                  '/'
                                ) | text
                              }}
                            </template>
                          </div>
                        </el-tooltip>
                      </template>
                    </el-table-column>
                    <el-table-column label="特价适用区域" minWidth="180">
                      <template #default="scope">
                        <el-tooltip
                          :disabled="
                            ObjectUtil.isNullOrBlank(scope.row.specialApplyMat.applicationScope)
                          "
                          popper-class="popper-class"
                          class="item"
                          effect="dark"
                          placement="top-start"
                        >
                          <div
                            slot="content"
                            style="white-space: pre-line"
                            v-html="formatSpecialPriceApplicableArea(scope.row)"
                          ></div>
                          <div class="table-cell">
                            <div>子公司： {{ scope.row.applicationScope.sbsdyName }}</div>
                            <div v-if="entity.specialType != 'adjustZoneGuidePrice'">
                              办事处：{{ scope.row.applicationScope.ofcName }}
                            </div>
                            <div class="text-ellipsis">
                              销往国：{{ formatCtryName(scope.row.applicationScope) }}
                            </div>
                            <div v-if="entity.specialType == 'importantProjects'">
                              客户：{{ scope.row.applicationScope.custName }}
                            </div>
                            <div>币种：{{ scope.row.applicationScope.currencyId }}</div>
                          </div>
                        </el-tooltip>
                      </template>
                    </el-table-column>
                    <el-table-column label="台量" minWidth="134">
                      <template #default="scoped">
                        <div class="table-cell" style="display: flex;">
                          {{ scoped.row.limitQty ? scoped.row.qty : '不限' }}
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column label="设备指导价/台" prop="exwPrice" minWidth="120">
                      <template #default="scoped">
                        <div
                          v-if="scoped.row.exwPrice > 0"
                          style="text-align: right"
                          class="table-cell-price"
                        >
                          <div style="display: inline-block">
                            {{ scoped.row.applicationScope.currencySymbol }}
                          </div>
                          <div style="display: inline-block">
                            {{ scoped.row.exwPrice | numberFilter('--', 0, true) }}
                          </div>
                        </div>
                        <div v-else style="text-align: right" class="table-cell-price">
                          <div style="display: inline-block">
                            {{ scoped.row.applicationScope.currencySymbol }}
                          </div>
                          <div style="display: inline-block">0</div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column minWidth="134">
                      <template #header>
                        <span>调价幅度</span>
                        <el-tooltip
                          content="系统自动计算，调价幅度=调价差值/EXW设备指导价%"
                          placement="top-start"
                        >
                          <i class="el-icon-info icon-info"></i>
                        </el-tooltip>
                      </template>
                      <template #default="scoped">
                        <div style="text-align: right; color: #606266;" class="table-cell-price">
                          {{ formatAdjustRange(scoped.row) }}
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column minWidth="160">
                      <template #header>
                        <span>EXW设备特价/台</span>
                        <el-tooltip
                          content="仅支持输入正整数，EXW设备特价=EXW设备指导价-调价差值"
                          placement="top-start"
                        >
                          <i class="el-icon-info icon-info"></i>
                        </el-tooltip>
                      </template>
                      <template #default="scoped">
                        <div style="text-align: right" class="table-cell-price">
                          <div style="display: inline-block">
                            {{ scoped.row.applicationScope.currencySymbol }}
                          </div>
                          <div style="display: inline-block">
                            {{ scoped.row.specialPrice | ceilFilter | numberFilter('--', 0, true) }}
                          </div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column minWidth="190">
                      <template #header>
                        <span>价格有效期</span>
                        <el-tooltip
                          content="申请特价的期望有效期，特价的实际有效期以审批通过后的价格有效期为准"
                          placement="top-start"
                        >
                          <i class="el-icon-info icon-info"></i>
                        </el-tooltip>
                      </template>
                      <template #default="scoped">
                        <div
                          style="text-align: left"
                          v-if="scoped.row.beginDate || scoped.row.endDate"
                        >
                          {{ scoped.row.beginDate }}～{{ scoped.row.endDate }}
                        </div>
                        <div style="text-align: left" v-else>
                          --
                        </div>
                      </template>
                    </el-table-column>
                    <!-- 包含物流费用及其他费用 -->
                    <el-table-column v-if="entity.otherExpenses" label="预估费用" align="center">
                      <el-table-column minWidth="120" label="预估物流费用/台">
                        <template #header>
                          <div style="text-align: center;">
                            <span>预估物流费用/台</span>
                            <el-tooltip
                              content="预估费用中计算的物流费用合计"
                              placement="top-start"
                            >
                              <i class="el-icon-info icon-info"></i>
                            </el-tooltip>
                          </div>
                        </template>
                        <template #default="scoped">
                          <div class="table-cell table-cell-price" style="display: flex;">
                            <div>
                              {{ scoped.row.applicationScope.currencySymbol }}
                            </div>
                            <div>
                              {{
                                scoped.row.logisticsExpenses
                                  | ceilFilter
                                  | numberFilter('--', 0, false)
                              }}
                            </div>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column minWidth="120" label="预估其他费用/台">
                        <template #header>
                          <div style="text-align: center;">
                            <span>预估其他费用/台</span>
                            <el-tooltip
                              content="预估费用中计算的其他费用合计"
                              placement="top-start"
                            >
                              <i class="el-icon-info icon-info"></i>
                            </el-tooltip>
                          </div>
                        </template>
                        <template #default="scoped">
                          <div class="table-cell" style="display: flex; align-items: center;">
                            <div style="display: flex;" class="table-cell-price">
                              <div>
                                {{ scoped.row.applicationScope.currencySymbol }}
                              </div>
                              <div>
                                {{
                                  scoped.row.otherExpenses
                                    | ceilFilter
                                    | numberFilter('--', 0, false)
                                }}
                              </div>
                            </div>
                            <!-- 额外费用用途信息图标 -->
                            <el-tooltip
                              v-if="hasExtraFeeUsageInProfit(scoped.row)"
                              :content="`含额外费用，用途：${getExtraFeeUsageInProfit(scoped.row)}`"
                              placement="top"
                              effect="dark"
                            >
                              <i class="el-icon-info icon-info" />
                            </el-tooltip>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column minWidth="120" label="预估综合特价/台">
                        <template #header>
                          <div style="text-align: center;">
                            <span>预估综合特价/台</span>
                            <el-tooltip
                              content="限制输入正整数，EXW设备特价/台=预估综合特价/台-预估物流费用/台-预估其他费用/台"
                              placement="top-start"
                            >
                              <i class="el-icon-info icon-info"></i>
                            </el-tooltip>
                          </div>
                        </template>
                        <template #default="scoped">
                          <div class="table-cell table-cell-price" style="display: flex;">
                            <div>
                              {{ scoped.row.applicationScope.currencySymbol }}
                            </div>
                            <div>
                              {{ scoped.row.total | numberFilter('--', 0, false) }}
                            </div>
                          </div>
                        </template>
                      </el-table-column>
                    </el-table-column>
                    <!-- 自营买断时，展示终端价格的费用项 -->
                    <el-table-column v-if="showPriceInfo" label="单台终端价格费用" align="center">
                      <el-table-column minWidth="120" label="终端价格">
                        <template #default="scoped">
                          <div
                            v-if="entity.supplementPriceCcyId"
                            class="table-cell"
                            style="display: flex;"
                          >
                            <div>
                              {{ entity.supplementPriceCcySymbol }}
                            </div>
                            <div>{{ scoped.row.terminalPrice | numberFilter('--', 0, false) }}</div>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column minWidth="130" label="经销商采购成本">
                        <template #default="scoped">
                          <div
                            v-if="entity.supplementPriceCcyId"
                            class="table-cell"
                            style="display: flex;"
                          >
                            <div>
                              {{ entity.supplementPriceCcySymbol }}
                            </div>
                            <div>
                              {{ scoped.row.dlrProcurementCost | numberFilter('--', 0, false) }}
                            </div>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column minWidth="120" label="内陆运费">
                        <template #default="scoped">
                          <div
                            v-if="entity.supplementPriceCcyId"
                            class="table-cell"
                            style="display: flex;"
                          >
                            <div>
                              {{ entity.supplementPriceCcySymbol }}
                            </div>
                            <div>{{ scoped.row.inlandFreight | numberFilter('--', 0, false) }}</div>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column minWidth="120" label="清关费">
                        <template #default="scoped">
                          <div
                            v-if="entity.supplementPriceCcyId"
                            class="table-cell"
                            style="display: flex;"
                          >
                            <div>
                              {{ entity.supplementPriceCcySymbol }}
                            </div>
                            <div>
                              {{ scoped.row.customsClearanceFee | numberFilter('--', 0, false) }}
                            </div>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column minWidth="120" label="组装费">
                        <template #default="scoped">
                          <div
                            v-if="entity.supplementPriceCcyId"
                            class="table-cell"
                            style="display: flex;"
                          >
                            <div>
                              {{ entity.supplementPriceCcySymbol }}
                            </div>
                            <div>{{ scoped.row.assemblyFee | numberFilter('--', 0, false) }}</div>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column minWidth="120" label="税费">
                        <template #default="scoped">
                          <div
                            v-if="entity.supplementPriceCcyId"
                            class="table-cell"
                            style="display: flex;"
                          >
                            <div>
                              {{ entity.supplementPriceCcySymbol }}
                            </div>
                            <div>{{ scoped.row.tax | numberFilter('--', 0, false) }}</div>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column minWidth="120" label="港杂/口岸费">
                        <template #default="scoped">
                          <div
                            v-if="entity.supplementPriceCcyId"
                            class="table-cell"
                            style="display: flex;"
                          >
                            <div>
                              {{ entity.supplementPriceCcySymbol }}
                            </div>
                            <div>{{ scoped.row.portCharge | numberFilter('--', 0, false) }}</div>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column minWidth="120" label="PDI费用">
                        <template #default="scoped">
                          <div
                            v-if="entity.supplementPriceCcyId"
                            class="table-cell"
                            style="display: flex;"
                          >
                            <div>
                              {{ entity.supplementPriceCcySymbol }}
                            </div>
                            <div>{{ scoped.row.pdiFee | numberFilter('--', 0, false) }}</div>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column minWidth="120" label="客户佣金">
                        <template #default="scoped">
                          <div
                            v-if="entity.supplementPriceCcyId"
                            class="table-cell"
                            style="display: flex;"
                          >
                            <div>
                              {{ entity.supplementPriceCcySymbol }}
                            </div>
                            <div>{{ scoped.row.commissionFee | numberFilter('--', 0, false) }}</div>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column minWidth="120" label="融资费用">
                        <template #default="scoped">
                          <div
                            v-if="entity.supplementPriceCcyId"
                            class="table-cell"
                            style="display: flex;"
                          >
                            <div>
                              {{ entity.supplementPriceCcySymbol }}
                            </div>
                            <div>
                              {{
                                scoped.row.financingFee | ceilFilter | numberFilter('--', 0, false)
                              }}
                            </div>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column minWidth="130" label="其他费用" class-name="table-form-item">
                        <template #default="scoped">
                          <div
                            v-if="entity.supplementPriceCcyId"
                            class="table-cell"
                            style="display: flex;"
                          >
                            <div>
                              {{ entity.supplementPriceCcySymbol }}
                            </div>
                            <div>
                              {{ scoped.row.otherFee | ceilFilter | numberFilter('--', 0, false) }}
                            </div>
                          </div>
                          <div v-if="entity.supplementPriceCcyId && scoped.row.otherFeeName">
                            {{ scoped.row.otherFeeName }}
                          </div>
                        </template>
                      </el-table-column>

                      <!-- FOB场景 -->
                      <el-table-column
                        v-if="entity.incotermsId === 'FOB'"
                        minWidth="130"
                        label="国际运费"
                        class-name="table-form-item"
                      >
                        <template #header>
                          <div style="text-align: center;">
                            <span>国际运费</span>
                            <el-tooltip
                              content="此列币种和该物料特价申请币种一致"
                              placement="top-start"
                            >
                              <i class="el-icon-info icon-info"></i>
                            </el-tooltip>
                          </div>
                        </template>
                        <template #default="scoped">
                          <div class="table-cell" style="display: flex;">
                            <div>
                              {{ scoped.row.applicationScope.currencySymbol }}
                            </div>
                            <div>
                              {{
                                getInternationalTransportationValue(
                                  scoped.row,
                                  scoped.row.applicationScope.currencyId
                                ) | numberFilter('--', 0, false)
                              }}
                            </div>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column
                        v-if="entity.incotermsId === 'FOB'"
                        minWidth="150"
                        label="国际运输保险费"
                        class-name="table-form-item"
                      >
                        <template #header>
                          <div style="text-align: center;">
                            <span>国际运输保险费</span>
                            <el-tooltip
                              content="此列币种和该物料特价申请币种一致"
                              placement="top-start"
                            >
                              <i class="el-icon-info icon-info"></i>
                            </el-tooltip>
                          </div>
                        </template>
                        <template #default="scoped">
                          <div class="table-cell" style="display: flex;">
                            <div>
                              {{ scoped.row.applicationScope.currencySymbol }}
                            </div>
                            <div>
                              {{
                                getInternationalTransportationPremiumValue(
                                  scoped.row,
                                  scoped.row.applicationScope.currencyId
                                ) | numberFilter('--', 0, false)
                              }}
                            </div>
                          </div>
                        </template>
                      </el-table-column>
                    </el-table-column>
                    <el-table-column label="单台毛利分析" minWidth="120" fixed="right">
                      <template #default="scope">
                        <el-button type="text" @click="viewGrossProfitAnalysisDetail(scope.row)"
                          >查看</el-button
                        >
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-tab-pane>
              <el-tab-pane v-if="showFeeDetails" label="费用明细" name="first">
                <div class="first-tab">
                  <div class="first-tab-summary">
                    <div>
                      共<span> {{ materialRowCount }} </span>行
                    </div>
                    <div>
                      <!-- <el-button
                        v-if="entity.otherExpenses === true"
                        type="text"
                        @click="doOtherExpenses"
                        style="margin-right: 20px"
                      >
                        查看其他费用明细
                      </el-button> -->
                      <!-- <el-button type="text" @click="doSpecialOffer"> 查看特价申请说明 </el-button> -->
                    </div>
                  </div>
                  <div class="first-tab-table">
                    <ExpenseDetails
                      v-if="entity.specialType"
                      ref="ExpenseDetails"
                      :baseEntity="entity"
                      :activeName="currentActiveName"
                    />
                  </div>
                </div>
              </el-tab-pane>
              <el-tab-pane v-if="entity.otherExpenses === true" label="付款方式" name="second">
                <div class="second-tab">
                  <el-table ref="table" :data="payTypeData" v-loading="loading">
                    <el-table-column label="付款方式" minWidth="280" prop="paymentTypeId">
                      <template #default="scoped">
                        {{ scoped.row.paymentTypeNameEn + '/' + scoped.row.paymentTypeName }}
                      </template>
                    </el-table-column>
                    <el-table-column label="付款比例" minWidth="104" prop="percentage">
                      <template #default="scoped">
                        {{
                          PrecisionUtil.floatMul(scoped.row.percentage, 100)
                            | numberFilter('--', 2, true)
                        }}%
                      </template>
                    </el-table-column>
                    <el-table-column label="天数" minWidth="80">
                      <template #default="scoped">
                        {{ scoped.row.days ? scoped.row.days : '--' }}
                        <!-- <number-input
                        v-if="scoped.row.payForward"
                        size="mini"
                        v-model="scoped.row.days"
                        xType="num"
                        @change="daysChange(scoped.row, scoped.$index)"
                      >
                      </number-input> -->
                        <!-- <div v-else>--</div> -->
                      </template>
                    </el-table-column>
                    <el-table-column label="远期贴现费率（年利率）" minWidth="124">
                      <template #default="scoped">
                        <div v-if="scoped.row.allowedForward">
                          <div>
                            <el-button
                              type="text"
                              @click="
                                checkSat(
                                  scoped.row.applicationScopeExpenseRate,
                                  'occForwardDiscountRate',
                                  '远期贴现费率（年利率）',
                                  scoped.row.days
                                )
                              "
                              >查看</el-button
                            >
                          </div>
                        </div>
                        <div v-else>--</div>
                      </template>
                    </el-table-column>
                    <el-table-column label="信保费率" minWidth="104">
                      <template #default="scoped">
                        <div v-if="scoped.row.allowedForward">
                          <div>
                            <el-button
                              type="text"
                              @click="
                                checkSat(
                                  scoped.row.applicationScopeExpenseRate,
                                  'occPremiumRate',
                                  '信保费率',
                                  scoped.row.days
                                )
                              "
                              >查看</el-button
                            >
                          </div>
                        </div>
                        <div v-else>--</div>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-tab-pane>
              <!-- <el-tab-pane v-if="showPriceInfo" label="价格信息" name="price">
                <div style="margin-bottom: 8px">币种：{{ priceEntity.supplementPriceCcyId }}</div>
                <el-table ref="table" :data="priceEntity.lines">
                  <el-table-column label="整机物料" minWidth="180" fixed="left">
                    <template #default="scoped">
                      <div>物料号：{{ scoped.row.specialApplyMat.matCd }}</div>
                      <div>国际产品线：{{ scoped.row.specialApplyMat.i18ProdGroupName }}</div>
                      <div>机型： {{ scoped.row.specialApplyMat.prodMdlCode }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column label="配置说明（营销）" minWidth="176">
                    <template #default="scope">
                      <el-tooltip
                        :disabled="
                          ObjectUtil.isNullOrBlank(scope.row.specialApplyMat) ||
                            ObjectUtil.isNullOrBlank(scope.row.specialApplyMat.matDesc)
                        "
                        popper-class="popper-class"
                        class="item"
                        effect="dark"
                        :content="
                          ObjectUtil.replaceStr(scope.row.specialApplyMat.matDesc, '@!', '/')
                        "
                        placement="top-start"
                      >
                        <div class="ellipsis-three-line">
                          {{
                            ObjectUtil.replaceStr(scope.row.specialApplyMat.matDesc, '@!', '/')
                              | text
                          }}
                        </div>
                      </el-tooltip>
                    </template>
                  </el-table-column>
                  <el-table-column label="配置说明（营销）_英文" minWidth="224">
                    <template #default="scope">
                      <el-tooltip
                        :disabled="
                          ObjectUtil.isNullOrBlank(scope.row.specialApplyMat) ||
                            ObjectUtil.isNullOrBlank(scope.row.specialApplyMat.matDescEn)
                        "
                        popper-class="popper-class"
                        class="item"
                        effect="dark"
                        :content="
                          ObjectUtil.replaceStr(scope.row.specialApplyMat.matDescEn, '@!', '/')
                        "
                        placement="top-start"
                      >
                        <div class="ellipsis-three-line">
                          {{
                            ObjectUtil.replaceStr(scope.row.specialApplyMat.matDescEn, '@!', '/')
                              | text
                          }}
                        </div>
                      </el-tooltip>
                    </template>
                  </el-table-column>
                  <el-table-column label="台量" prop="qty" minWidth="50">
                    <template #default="scoped">
                      <div style="font-weight: 800;">
                        {{ scoped.row.limitQty ? scoped.row.qty : '不限' }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="单台费用" align="center">
                    <el-table-column minWidth="120" label="终端价格">
                      <template #default="scoped">
                        <div v-if="entity.supplementPriceCcyId" class="table-cell-price">
                          <div>
                            {{ entity.supplementPriceCcySymbol }}
                          </div>
                          <div>{{ scoped.row.terminalPrice | numberFilter('--', 0, false) }}</div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column minWidth="130" label="经销商采购成本">
                      <template #default="scoped">
                        <div v-if="entity.supplementPriceCcyId" class="table-cell-price">
                          <div>
                            {{ entity.supplementPriceCcySymbol }}
                          </div>
                          <div>
                            {{ scoped.row.dlrProcurementCost | numberFilter('--', 0, false) }}
                          </div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column minWidth="120" label="内陆运费">
                      <template #default="scoped">
                        <div v-if="entity.supplementPriceCcyId" class="table-cell-price">
                          <div>
                            {{ entity.supplementPriceCcySymbol }}
                          </div>
                          <div>{{ scoped.row.inlandFreight | numberFilter('--', 0, false) }}</div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column minWidth="120" label="清关费">
                      <template #default="scoped">
                        <div v-if="entity.supplementPriceCcyId" class="table-cell-price">
                          <div>
                            {{ entity.supplementPriceCcySymbol }}
                          </div>
                          <div>
                            {{ scoped.row.customsClearanceFee | numberFilter('--', 0, false) }}
                          </div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column minWidth="120" label="组装费">
                      <template #default="scoped">
                        <div v-if="entity.supplementPriceCcyId" class="table-cell-price">
                          <div>
                            {{ entity.supplementPriceCcySymbol }}
                          </div>
                          <div>{{ scoped.row.assemblyFee | numberFilter('--', 0, false) }}</div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column minWidth="120" label="税费">
                      <template #default="scoped">
                        <div v-if="entity.supplementPriceCcyId" class="table-cell-price">
                          <div>
                            {{ entity.supplementPriceCcySymbol }}
                          </div>
                          <div>{{ scoped.row.tax | numberFilter('--', 0, false) }}</div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column minWidth="120" label="港杂/口岸费">
                      <template #default="scoped">
                        <div v-if="entity.supplementPriceCcyId" class="table-cell-price">
                          <div>
                            {{ entity.supplementPriceCcySymbol }}
                          </div>
                          <div>{{ scoped.row.portCharge | numberFilter('--', 0, false) }}</div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column minWidth="120" label="PDI费用">
                      <template #default="scoped">
                        <div v-if="entity.supplementPriceCcyId" class="table-cell-price">
                          <div>
                            {{ entity.supplementPriceCcySymbol }}
                          </div>
                          <div>{{ scoped.row.pdiFee | numberFilter('--', 0, false) }}</div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column minWidth="120" label="客户佣金">
                      <template #default="scoped">
                        <div v-if="entity.supplementPriceCcyId" class="table-cell-price">
                          <div>
                            {{ entity.supplementPriceCcySymbol }}
                          </div>
                          <div>{{ scoped.row.commissionFee | numberFilter('--', 0, false) }}</div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column minWidth="120" label="融资费用">
                      <template #default="scoped">
                        <div v-if="entity.supplementPriceCcyId" class="table-cell-price">
                          <div>
                            {{ entity.supplementPriceCcySymbol }}
                          </div>
                          <div>{{ scoped.row.financingFee | numberFilter('--', 0, false) }}</div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column minWidth="130" label="其他费用" class-name="table-form-item">
                      <template #default="scoped">
                        <div v-if="entity.supplementPriceCcyId" class="table-cell-price">
                          <div>
                            {{ entity.supplementPriceCcySymbol }}
                          </div>
                          <div>{{ scoped.row.otherFee | numberFilter('--', 0, false) }}</div>
                        </div>
                        <div v-if="entity.supplementPriceCcyId && scoped.row.otherFeeName">
                          {{ scoped.row.otherFeeName }}
                        </div>
                      </template>
                    </el-table-column>
                  </el-table-column>
                </el-table>
              </el-tab-pane> -->
              <!-- <el-tab-pane label="审批信息" name="fourth">
                <div class="fourth-tab">
                  <el-table :data="bpfmHiTaskInstList" max-height="500">
                    <el-table-column label="任务名称">
                      <template slot-scope="scope">
                        <div class="task">
                          <div
                            :class="scope.row.nodeMode === 'one' ? 'normal-state' : 'special-state'"
                          >
                            {{ scope.row.nodeMode === 'one' ? '或签' : '会签' }}
                          </div>
                          <div class="task-name">{{ scope.row.taskName | text }}</div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column label="候选人/执行人">
                      <template slot-scope="scope">
                        <div>
                          {{ scope.row.candidate.name | text }}[{{
                            scope.row.candidate.code | text
                          }}]
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column label="执行操作">
                      <template slot-scope="scope">
                        <div>{{ (scope.row.outgoing && scope.row.outgoing.name) | text }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column label="审批意见">
                      <template slot-scope="scope">
                        <el-tooltip
                          popper-class="popper-class"
                          class="item"
                          effect="dark"
                          :content="scope.row.comment | text"
                          placement="top-start"
                        >
                          <div class="ellipsis-two-line">
                            {{ scope.row.comment | text }}
                          </div>
                        </el-tooltip>
                      </template>
                    </el-table-column>
                    <el-table-column label="任务抵达时间">
                      <template slot-scope="scope">
                        <div>{{ scope.row.createTime | text }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column label="任务完成时间">
                      <template slot-scope="scope">
                        <div>{{ scope.row.endTime | text }}</div>
                      </template>
                    </el-table-column>
                    <el-table-column label="执行时长（H）">
                      <template slot-scope="scope">
                        <div>{{ handleTime(scope.row.durationInSeconds) }}</div>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-tab-pane> -->
              <el-tab-pane
                v-if="uploadNormalFile || uploadPrivacyFile || isHasviewProfit"
                label="附件"
                name="fifth"
              >
                <div class="fifth-tab">
                  <el-tabs v-model="activeNameEx">
                    <el-tab-pane
                      v-if="uploadNormalFile || uploadPrivacyFile || isHasviewProfit"
                      label="普通附件"
                      name="normal"
                    >
                      <el-upload action="" :before-upload="onUpload1" :show-file-list="false">
                        <el-button size="mini" style="margin-bottom: 8px">上传附件</el-button>
                      </el-upload>
                      <el-table v-loading="loading3" :data="fileList3" maxHeight="350px">
                        <el-table-column label="附件名称" minWidth="240">
                          <template #default="scope">
                            <div
                              v-if="showPreview(scope.row)"
                              style="color: #3b71fc"
                              @click="previewFile(scope.row)"
                            >
                              {{ scope.row.fileName }}
                            </div>
                            <div v-else>
                              {{ scope.row.fileName }}
                            </div>
                          </template>
                        </el-table-column>
                        <el-table-column label="类型" minWidth="120">
                          <template #default>
                            <div>普通附件</div>
                          </template>
                        </el-table-column>
                        <el-table-column label="上传信息" minWidth="240">
                          <template #default="scope">
                            {{ scope.row.createUserName + '/' + scope.row.createTime }}
                          </template>
                        </el-table-column>
                        <el-table-column label="操作" width="128">
                          <template #default="scope">
                            <el-button type="text" @click="onDownload(scope.row)">下载</el-button>
                            <el-button type="text" @click="onDelete(scope.row)">删除</el-button>
                          </template>
                        </el-table-column>
                      </el-table>
                    </el-tab-pane>
                    <el-tab-pane
                      v-if="uploadPrivacyFile || isHasviewProfit"
                      label="保密附件"
                      name="privacy"
                    >
                      <el-upload action="" :before-upload="onUpload2" :show-file-list="false">
                        <el-button size="mini" style="margin-bottom: 8px">上传附件</el-button>
                      </el-upload>
                      <el-table v-loading="loading4" :data="fileList4" maxHeight="350px">
                        <el-table-column label="附件名称" minWidth="240">
                          <template #default="scope">
                            <div
                              v-if="showPreview(scope.row)"
                              style="color: #3b71fc"
                              @click="previewFile(scope.row)"
                            >
                              {{ scope.row.fileName }}
                            </div>
                            <div v-else>
                              {{ scope.row.fileName }}
                            </div>
                          </template>
                        </el-table-column>
                        <el-table-column label="类型" minWidth="120">
                          <template #default>
                            <div>保密附件</div>
                          </template>
                        </el-table-column>
                        <el-table-column label="上传信息" minWidth="240">
                          <template #default="scope">
                            {{ scope.row.createUserName + '/' + scope.row.createTime }}
                          </template>
                        </el-table-column>
                        <el-table-column label="操作" width="128">
                          <template #default="scope">
                            <el-button type="text" @click="onDownload(scope.row)">下载</el-button>
                            <el-button type="text" @click="onDelete(scope.row)">删除</el-button>
                          </template>
                        </el-table-column>
                      </el-table>
                    </el-tab-pane>
                  </el-tabs>
                </div>
              </el-tab-pane>
            </el-tabs>
          </panel>
        </div>
      </div>
      <DialogCheck
        v-model="dialogDialogCheck"
        :childrenEntity="childrenEntity"
        :baseEntity="entity"
        :childrenType="childrenType"
        :childrenTitle="childrenTitle"
        :days="days"
      ></DialogCheck>
      <!-- <OtherDialog v-model="OtherDialogShow" :baseEntity="entity"></OtherDialog> -->
      <ApprovalInformationDrawer
        ref="approvalInformationDrawer"
        v-model="showApprovalInformationDrawer"
        :baseEntity="entity"
        :id="id"
      >
      </ApprovalInformationDrawer>
      <SpecialGrossProfitAnalysisDrawer
        v-model="showGrossProfitAnalysisDrawer"
        :baseEntity="entity"
        :data="grossProfitAnalysisDrawerInfo"
        :exchangeRateOptions="exchangeRateTypeList"
      >
      </SpecialGrossProfitAnalysisDrawer>
    </template>
  </detail-page>
</template>

<script src="./SpecialApplyDetail.ts"></script>

<style lang="scss" scoped>
.detail {
  padding: 12px;

  &-title {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    /deep/ .state {
      border-radius: 4px;
      margin-right: 8px;
    }

    &-num {
      font-weight: bold;
      font-size: 16px;
      color: #242633;
      margin-right: 4px;
    }

    i {
      color: #de3232;
      font-size: 22px;
    }
  }

  &-tip {
    display: flex;
    align-items: center;
    color: #ffa001;
    background: #fff5e6;
    border-radius: 2px;
    border: 1px solid #ffd999;
    padding: 8px 12px;
    margin-bottom: 12px;

    i {
      font-size: 18px;
      color: #ffa001;
      margin-right: 8px;
    }

    &-text {
      word-break: break-all;
    }
  }

  &-main {
    .bottom-margin {
      margin-bottom: 16px;
    }

    .detail-btn {
      font-size: 12px;
      color: #3b71fc;
      line-height: 18px;
      cursor: pointer;
    }

    /deep/ .el-tabs__header {
      margin-bottom: 8px;
    }

    .first-tab {
      &-summary {
        height: 24px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        span {
          font-size: 14px;
          font-weight: bold;
        }
      }

      &-tip {
        height: 44px;
        background: #fff5e6;
        border-radius: 4px;
        margin-bottom: 8px;

        &-item {
          display: flex;
          align-items: center;
          justify-content: center;
        }

        span {
          font-size: 14px;
          font-weight: bold;
          margin: 0px 8px;
        }
      }
    }

    .third-tab {
      &-tip {
        height: 44px;
        background: #fff5e6;
        border-radius: 4px;
        margin-bottom: 8px;

        &-item {
          display: flex;
          align-items: center;
          justify-content: center;
        }

        span {
          font-size: 14px;
          font-weight: bold;
          margin: 0px 8px;
        }
      }
    }

    .fourth-tab {
      .task {
        display: flex;

        .task-name {
          padding-left: 10px;
        }

        .normal-state {
          color: #ffa001;
        }

        .special-state {
          color: #4ac0f4;
        }
      }
    }

    .textLength {
      white-space: normal;
      word-break: break-all;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 4; // 代表文本超长最多显示2行，可自行调整最多显示的行数
      -webkit-box-orient: vertical;
    }

    .icon-info {
      margin-top: 4px;
      color: #a1b0c8;
      font-size: 14px;
      margin: 0px 4px;

      &:hover {
        color: $--color-primary;
      }
    }

    .table-cell-price {
      display: flex;
      font-weight: 800;
    }
  }

  .textBlod {
    font-weight: 800;
  }
}

/deep/ .el-table__header-wrapper .el-table__cell {
  // 以下为UI样式
  padding-top: 6px;
  padding-bottom: 8px;
}

/deep/ .el-table__fixed-header-wrapper .el-table__cell {
  // 以下为UI样式
  padding-top: 6px;
  padding-bottom: 8px;
}

/deep/ .el-table__fixed-body-wrapper .el-table__cell {
  // 以下为UI样式
  padding-top: 6px;
  padding-bottom: 8px;
}

/deep/ .el-table__body-wrapper .el-table__cell {
  // 以下为UI样式
  padding-top: 6px;
  padding-bottom: 8px;
}

.first-tab-table {
  /deep/ .el-table__fixed-body-wrapper .el-table__cell {
    // 以下为UI样式
    padding-top: 0px;
    padding-bottom: 0px;
  }

  /deep/ .el-table__body-wrapper .el-table__cell {
    // 以下为UI样式
    padding-top: 0px;
    padding-bottom: 0px;
  }

  .icon-info {
    margin-top: 4px;
    color: #a1b0c8;
    font-size: 14px;
    margin: 0px 4px;
    &:hover {
      color: $--color-primary;
    }
  }
}
</style>
