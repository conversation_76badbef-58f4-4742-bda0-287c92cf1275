<!--
 * @Author: 张文轩
 * @Date: 2024-06-27 17:22:03
 * @LastEditTime: 2024-09-03 09:59:55
 * @LastEditors: 张文轩
 * @Description:
 * @FilePath: \ltc-web-ui\src\views\data\ContractQuotation\cmp\QuotationEditStep5.vue
 * 记得注释
-->
<template>
  <div class="step5">
    <panel class="step5-form" header="基本信息">
      <el-row :gutter="24">
        <el-col :span="12" class="bottom-margin">
          <view-item label="报价主题 :">
            <div class="text-icon">
              <span>{{ baseEntity.topic }}</span>
              <img
                v-if="baseEntity.urgentApprove"
                src="@/assets/images/components/hd-steps/icon.png"
                width="18px"
                height="18px"
              />
            </div>
          </view-item>
        </el-col>
        <el-col :span="12" class="bottom-margin">
          <view-item label="客户 :">
            {{ '[' + baseEntity.custCode + ']' + baseEntity.custName }}
          </view-item>
        </el-col>
        <el-col :span="6" class="bottom-margin">
          <view-item label="办事处 :"> {{ baseEntity.ofcName }}</view-item>
        </el-col>
        <el-col :span="6" class="bottom-margin">
          <view-item label="子公司 :">
            {{ baseEntity.sbsdyName }}
          </view-item>
        </el-col>
        <el-col :span="6" class="bottom-margin">
          <view-item label="销往国 :">
            {{ baseEntity.ctryName }}
          </view-item>
        </el-col>
        <el-col :span="6" class="bottom-margin">
          <view-item label="出口类型 :">
            {{ baseEntity.exportTypeId | exportTypeFormatter }}
          </view-item>
        </el-col>
        <el-col v-if="baseEntity.exportTypeId !== 'supply'" :span="6" class="bottom-margin">
          <view-item label="销售模式 :">
            {{ baseEntity.saleMode | saleModeFormatter }}
          </view-item>
        </el-col>
        <el-col :span="6" class="bottom-margin">
          <view-item label="币种 :">
            {{ '[' + baseEntity.currencyId + ']' + baseEntity.currencyName }}
          </view-item>
        </el-col>
        <el-col v-if="baseEntity.exportTypeId !== 'supply'" :span="6" class="bottom-margin">
          <view-item label="贸易术语 :">
            {{ baseEntity.incotermsId }}
          </view-item>
        </el-col>
        <el-col :span="6" class="bottom-margin">
          <view-item label="预计签署日期 :">
            {{ baseEntity.signDate | DateFormatter }}
          </view-item>
        </el-col>
        <el-col :span="6">
          <view-item label="预计发运日期 :">
            {{ baseEntity.deliveryDate | DateFormatter }}
          </view-item>
        </el-col>
      </el-row>
    </panel>
    <panel header="价格与费用" style="margin-bottom: 0px;"> </panel>
    <div class="step5-main">
      <div v-if="baseEntity.saleMode !== 'buyOut'" class="step5-main-tab">
        <div
          :class="['step5-main-tab-item', { 'step5-main-tab-active': activeTab === item.step }]"
          v-for="item in tabList"
          :key="item.step"
          @click="handleTabClick(item.step)"
        >
          <!-- <div class="item-icon">
            <div>{{ item.step }}</div>
          </div> -->
          <div class="item-text">{{ item.name }}</div>
        </div>
      </div>
      <div class="step5-main-content">
        <TabStep1
          v-if="activeTab === 1"
          ref="step5tab1"
          :baseEntity="baseEntity"
          @summary="getSummary"
        />
        <TabStep2 v-if="activeTab === 2" ref="step5tab2" :baseEntity="baseEntity" />
        <TabStep3 v-if="activeTab === 3" ref="step5tab3" :baseEntity="baseEntity" />
      </div>
    </div>
    <div class="step5-footer">
      <div class="step5-footer-summary">
        <div class="summary-item">
          <div class="summary-item-label">整单设备总指导价</div>
          <div class="summary-item-value">
            <div>{{ baseEntity.currencySymbol }}</div>
            <div>{{ summaryInfo.exwPrice | numberFilter('--', 0, true) }}</div>
          </div>
        </div>
        <div class="summary-item">
          <div class="summary-item-label">整单设备总报价</div>
          <div class="summary-item-value">
            <div>{{ baseEntity.currencySymbol }}</div>
            <div>{{ summaryInfo.actualPrice | numberFilter('--', 0, true) }}</div>
          </div>
        </div>
        <div class="summary-item">
          <div class="summary-item-label">运杂费合计</div>
          <div class="summary-item-value">
            <div>{{ baseEntity.currencySymbol }}</div>
            <div>{{ summaryInfo.domesticInternationalFreight | numberFilter('--', 0, true) }}</div>
          </div>
          <div class="summary-item-btn" @click="allFreightClick">
            查看明细
          </div>
        </div>
        <div class="summary-item">
          <div class="summary-item-label">其他费用合计</div>
          <div class="summary-item-value">
            <div>{{ baseEntity.currencySymbol }}</div>
            <div>{{ summaryInfo.otherFreight | numberFilter('--', 0, true) }}</div>
          </div>
          <div class="summary-item-btn" @click="allOtherFreightLineClick">
            查看明细
          </div>
        </div>
        <div class="summary-item">
          <div class="summary-item-label">整单综合报价合计</div>
          <div class="summary-item-value">
            <div>{{ baseEntity.currencySymbol }}</div>
            <div>{{ summaryInfo.quotationTotal | numberFilter('--', 0, true) }}</div>
          </div>
        </div>
      </div>
    </div>
    <OtherFreightLineDrawer
      v-model="showOtherFreightLineDrawer"
      :baseEntity="baseEntity"
      :otherFeeNameList="otherFeeNameList"
    >
    </OtherFreightLineDrawer>
    <FreightDrawer v-model="showFreightDrawer" :firstList="firstList" :baseEntity="baseEntity">
    </FreightDrawer>
  </div>
</template>

<script src="./QuotationEditStep5.ts"></script>

<style lang="scss" scoped>
.step5 {
  height: 100%;
  display: flex;
  flex-direction: column;

  &-main {
    flex: 1;
    display: flex;
    border-radius: 4px;
    border: 1px solid #d7dfeb;
    margin-bottom: 12px;
    &-tab {
      flex-shrink: 0;
      flex-grow: 0;
      width: 160px;
      color: #79879e;
      background-color: #f7f9fc;

      &-item {
        display: flex;
        align-items: center;
        height: 56px;
        padding: 0px 12px;
        cursor: pointer;
        .item-icon {
          width: 20px;
          height: 20px;
          line-height: 20px;
          text-align: center;
          border-radius: 10px;
          background-color: #e5e8eb;
          margin-right: 12px;
        }
        .item-text {
          font-size: 14px;
        }
      }

      &-active {
        background-color: #fff;
        .item-icon {
          font-weight: bold;
          color: #fff;
          background-color: $--color-primary;
        }
        .item-text {
          font-weight: bold;
          color: $--color-primary;
        }
      }
    }

    &-content {
      width: 0px;
      flex-grow: 1;
      flex-shrink: 1;
      padding: 12px;
    }
  }

  &-footer {
    display: flex;
    margin: 0px -12px;
    padding: 7px 12px;
    background-color: #fff5e6;
    border-top: solid 2px #ffa001;

    &-summary {
      display: flex;
      flex: 1;

      .summary-item {
        display: flex;
        flex: 1; // Equal distribution
        line-height: 28px;
        min-width: 0; // Prevent overflow
        justify-content: center;

        &-label {
          flex-shrink: 0;
          color: #36445a;
          text-align: right;
          margin-right: 8px;
          white-space: nowrap; // Prevent label wrapping
        }
        &-value {
          display: flex;
          flex-shrink: 0; // 改为不收缩，让 justify-content: flex-end 生效
          color: #242633;
          font-weight: bold;
          text-align: left;
          min-width: 0; // Prevent overflow
        }
        &-btn {
          font-weight: 600;
          color: #3b71fc;
          cursor: pointer;
          margin-left: 8px;
        }
      }
    }
  }

  .text-icon {
    display: flex;
    align-items: center;
    span {
      margin-right: 4px;
    }
  }
  .bottom-margin {
    margin-bottom: 16px;
  }
}
</style>
