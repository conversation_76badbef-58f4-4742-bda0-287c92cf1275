import ApplyDescription from 'model/remote/price/api/specialapply/bill/ApplyDescription'
import SpecialApplyPriceOverviewMatLineV2 from 'model/remote/price/api/specialapply/bill/SpecialApplyPriceOverviewMatLineV2'
import UserTaskOutGoing from 'model/remote/price/api/quotationapply/bill/bpfm/UserTaskOutGoing'

// 设备特价申请单特价总览保存请求
export default class SpecialApplyPriceOverviewSaveRequestV2 {
  // ID,特价申请单号
  owner: string = ''
  // 版本
  version: number = 0
  // 报价是否包含以下费用项:物流费用、延保费、客户佣金、赠送配件金额、额外费用、远期贴现费、信保费、国内运输保险费、国际运输保险费
  otherExpenses: Nullable<boolean> = null
  // 应用描述
  applyDescription: Nullable<ApplyDescription> = null
  //  物料范围
  matLines: SpecialApplyPriceOverviewMatLineV2[] = []
  // 流程出口. 非新建场景，get接口有且仅会返回一个出口
  outGoing: Nullable<UserTaskOutGoing> = null
}