import SpecialApplyOtherExpensesMatLine from 'model/remote/price/api/specialapply/otherexpenses/SpecialApplyOtherExpensesMatLine'

// 设备特价申请单其他费用信息物料信息明细保存请求
export default class SpecialApplyOtherExpensesMatLineSaveRequest {
  // 所属特价申请单, SpecialApplyBill.id
  owner: string = ''
  // 版本
  version: number = 0
  // 国内运输保险费是否投保。供货可以选择否
  domesticTransInsurancePremium: boolean = false
  // 设备特价申请单其他费用信息物料信息明细
  lines: SpecialApplyOtherExpensesMatLine[] = []
  // 是否检查,默认检查
  check: Nullable<boolean> = null
}