import SpecialApplyApplicationScopePayType from 'model/remote/price/api/specialapply/otherexpenses/SpecialApplyApplicationScopePayType'

// 支付方式保存请求
export default class SpecialApplyPayTypeSaveRequest {
  // 所属特价申请单, SpecialApplyBill.id
  owner: string = ''
  // 版本
  version: number = 0
  // 支付方式明细信息
  lines: SpecialApplyApplicationScopePayType[] = []
  // 是否检查,默认检查
  check: Nullable<boolean> = null
}