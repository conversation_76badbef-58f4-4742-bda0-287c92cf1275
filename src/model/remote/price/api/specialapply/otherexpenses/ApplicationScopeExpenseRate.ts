import ApplicationScope from 'model/remote/price/api/specialapply/bill/ApplicationScope'

// 设备特价申请应用范围费率
export default class ApplicationScopeExpenseRate {
  //  应用范围
  applicationScop: Nullable<ApplicationScope> = null
  // 客户是否柳工子公司
  subsidiary: Nullable<boolean> = null
  //  当前远期贴现费率,后端重新计算
  occForwardDiscountRate: Nullable<number> = null
  //  申请远期贴现费率,后端重新计算
  applyForwardDiscountRate: Nullable<number> = null
  //  是否需要计算费率，true：需要计算 false：不需要计算 
  calForwardDiscountRate: Nullable<boolean> = null
  //  当前信保率
  occPremiumRate: Nullable<number> = null
  //  申请信保率
  applyPremiumRate: Nullable<number> = null
  //  是否需要计算费率，true：需要计算 false：不需要计算 
  calPremiumRate: Nullable<boolean> = null
}