import ApplicationScopeExpenseRate from 'model/remote/price/api/specialapply/otherexpenses/ApplicationScopeExpenseRate'

// 设备特价单应用范围付款方式
export default class SpecialApplyApplicationScopePayType {
  // 排序
  line: Nullable<number> = null
  // ID
  id: Nullable<string> = null
  // 所属特价申请单, SpecialApplyBill.id
  owner: Nullable<string> = null
  //  付款方式 付款方式ID
  paymentTypeId: Nullable<string> = null
  //  支付方式名称
  paymentTypeName: Nullable<string> = null
  // 支付方式名称
  paymentTypeNameEn: Nullable<string> = null
  //  付款百分比
  percentage: Nullable<number> = null
  // 是否允许远期
  allowedForward: Nullable<boolean> = null
  //  付款天数
  days: Nullable<number> = null
  //  应用范围费率
  applicationScopeExpenseRate: ApplicationScopeExpenseRate[] = []
}