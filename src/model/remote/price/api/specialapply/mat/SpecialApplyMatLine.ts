import SpecialApplyMatLineScopeExwPrice from 'model/remote/price/api/specialapply/mat/SpecialApplyMatLineScopeExwPrice'

// 特价申请单物料
export default class SpecialApplyMatLine {
  // ID
  id: Nullable<string> = null
  // 所属特价申请单, SpecialApplyBill.id
  owner: Nullable<string> = null
  //  行号
  line: Nullable<number> = null
  //  物料号
  matCd: Nullable<string> = null
  // 国际产品组id
  i18ProdGroupId: Nullable<string> = null
  // 国际产品线名称
  i18ProdGroupName: Nullable<string> = null
  // 机型ID
  prodMdlId: Nullable<string> = null
  // 机型代码
  prodMdlCode: Nullable<string> = null
  // 物料描述
  matDesc: Nullable<string> = null
  // 物料描述(英文)
  matDescEn: Nullable<string> = null
  //  是否限制数量
  limitQty: Nullable<boolean> = null
  //  限量台量
  qty: Nullable<number> = null
  // 成本价（CNY）
  costPrice: Nullable<number> = null
  // 区域指导价
  exwPrice: SpecialApplyMatLineScopeExwPrice[] = []
}