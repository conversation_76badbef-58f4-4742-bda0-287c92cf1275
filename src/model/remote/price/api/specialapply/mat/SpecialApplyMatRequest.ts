import SpecialApplyExwPriceRequestMatLineInfo from 'model/remote/price/api/specialapply/mat/SpecialApplyExwPriceRequestMatLineInfo'
import { MatApplyType } from 'model/remote/price/api/specialapply/bill/MatApplyType'

// 物料特价申请单物料信息
export default class SpecialApplyMatRequest {
  // ID,存储单号
  id: string = ''
  // 版本
  version: number = 0
  // 物料明细
  lines: SpecialApplyExwPriceRequestMatLineInfo[] = []
  // 是否检查指导价,默认检查
  checkExwPrice: Nullable<boolean> = null
  // 物料申请类型
  applyType: Nullable<MatApplyType> = null
}