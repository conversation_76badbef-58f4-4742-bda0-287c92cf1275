import SpecialApplyMatLine from 'model/remote/price/api/specialapply/mat/SpecialApplyMatLine'
import { MatApplyType } from 'model/remote/price/api/specialapply/bill/MatApplyType'

// 物料特价申请单物料信息响应
export default class SpecialApplyMatResponse {
  // ID,存储单号
  id: string = ''
  // 版本
  version: number = 0
  // 物料申请类型
  applyType: Nullable<MatApplyType> = null
  // 物料明细
  lines: SpecialApplyMatLine[] = []
}