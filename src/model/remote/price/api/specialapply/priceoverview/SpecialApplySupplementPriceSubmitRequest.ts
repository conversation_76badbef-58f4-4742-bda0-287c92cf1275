import SpecialApplySupplementPrice from 'model/remote/price/api/specialapply/priceoverview/SpecialApplySupplementPrice'
import UserTaskOutGoing from 'model/remote/price/api/quotationapply/bill/bpfm/UserTaskOutGoing'

// 特价申请补充价格信息提交请求
export default class SpecialApplySupplementPriceSubmitRequest extends SpecialApplySupplementPrice {
  // 流程出口. 非新建场景，get接口有且仅会返回一个出口
  outGoing: Nullable<UserTaskOutGoing> = null
}