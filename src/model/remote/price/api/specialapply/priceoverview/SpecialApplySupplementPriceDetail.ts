import SpecialApplyCurrency from 'model/remote/price/api/specialapply/otherexpenses/SpecialApplyCurrency'
import SpecialApplyFee from 'model/remote/price/api/specialapply/otherexpenses/SpecialApplyFee'
import SpecialApplyMatLine from 'model/remote/price/api/specialapply/mat/SpecialApplyMatLine'

// 补充价格信息明细
export default class SpecialApplySupplementPriceDetail {
  // ID
  id: Nullable<string> = null
  // owner
  owner: Nullable<string> = null
  // ownerMat
  ownerMat: Nullable<string> = null
  //  物料信息
  specialApplyMat: Nullable<SpecialApplyMatLine> = null
  // 终端价格/台
  terminalPrice: Nullable<number> = null
  // 经销商采购成本/台
  dlrProcurementCost: Nullable<number> = null
  // 内陆运费/台
  inlandFreight: Nullable<number> = null
  // 清关费/台
  customsClearanceFee: Nullable<number> = null
  // 组装费/台
  assemblyFee: Nullable<number> = null
  // 税费/台
  tax: Nullable<number> = null
  // 港杂口岸费/台
  portCharge: Nullable<number> = null
  // PDI费用/台
  pdiFee: Nullable<number> = null
  // 客户佣金/台
  commissionFee: Nullable<number> = null
  // 融资费用/台
  financingFee: Nullable<number> = null
  // 其他费用/台
  otherFee: Nullable<number> = null
  // 其他费用费用项
  otherFeeName: Nullable<string> = null
  // 币种清单。不为空，用于前端展示国际运费、国际运输保险费枚举
  currencyIds: SpecialApplyCurrency[] = []
  // FOB-币种维度-国际运费明细
  internationalTransportations: SpecialApplyFee[] = []
  // FOB-币种维度-国际运输保险费明细
  internationalTransportationPremiums: SpecialApplyFee[] = []
}