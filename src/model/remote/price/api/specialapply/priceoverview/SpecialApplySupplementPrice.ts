import SpecialApplySupplementPriceDetail from 'model/remote/price/api/specialapply/priceoverview/SpecialApplySupplementPriceDetail'

// 特价申请补充价格信息
export default class SpecialApplySupplementPrice {
  // ID,特价申请单号
  owner: Nullable<string> = null
  // 版本
  version: Nullable<number> = null
  // 补充价格币种id ，来自数据字典
  supplementPriceCcyId: Nullable<string> = null
  // 补充价格币种名称，来自数据字典
  supplementPriceCcyName: Nullable<string> = null
  // 补充价格币种符号，来自数据字典
  supplementPriceCcySymbol: Nullable<string> = null
  // 补充价格明细行
  lines: SpecialApplySupplementPriceDetail[] = []
}