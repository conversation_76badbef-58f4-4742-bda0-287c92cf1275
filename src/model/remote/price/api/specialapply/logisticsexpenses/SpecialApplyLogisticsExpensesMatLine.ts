import SpecialApplyCurrency from 'model/remote/price/api/specialapply/otherexpenses/SpecialApplyCurrency'
import SpecialApplyFee from 'model/remote/price/api/specialapply/otherexpenses/SpecialApplyFee'
import SpecialApplyMatLine from 'model/remote/price/api/specialapply/mat/SpecialApplyMatLine'

// 设备特价申请单物流费用物料信息明细
export default class SpecialApplyLogisticsExpensesMatLine {
  // ID
  id: Nullable<string> = null
  // 所属特价申请单, SpecialApplyBill.id
  owner: Nullable<string> = null
  // 所属设备特价申请单物料明细, SpecialApplyMatLine.id
  ownerMat: Nullable<string> = null
  // 物料明细
  matLine: Nullable<SpecialApplyMatLine> = null
  // 发货地/出发地代码
  domesticFreightLandCode: Nullable<string> = null
  // 发货地/出发地名称
  domesticFreightLandName: Nullable<string> = null
  // 自营-起运港/起运站代码
  domesticFreightPortCode: Nullable<string> = null
  // 自营-起运港/起运站代码
  domesticFreightPortName: Nullable<string> = null
  // 币种清单。不为空，用于前端展示供货装箱费枚举
  currencyIds: SpecialApplyCurrency[] = []
  // 供货-币种维度-装箱费明细
  packingFees: SpecialApplyFee[] = []
}