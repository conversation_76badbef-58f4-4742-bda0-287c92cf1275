import SpecialApplyLogisticsExpensesMatLine from 'model/remote/price/api/specialapply/logisticsexpenses/SpecialApplyLogisticsExpensesMatLine'

// 设备特价申请单物流费用物料信息
export default class SpecialApplyLogisticsExpensesMatInfo {
  // 所属特价申请单, SpecialApplyBill.id
  owner: string = ''
  // 版本
  version: number = 0
  // 自营-贸易术语，来自数据字典
  incotermsId: Nullable<string> = null
  // 自营-国际运输模式，来自数据字典
  transportTypeId: Nullable<string> = null
  // 自营-国际运输模式类型-滚装、散杂，来自数据字典
  transportTypeRoleId: Nullable<string> = null
  // 自营-目的港目的站 代码
  destinationPortCode: Nullable<string> = null
  // 自营-目的港目的站 名称
  destinationPortName: Nullable<string> = null
  // 供货-交货地点 代码
  domesticFreightLandCode: Nullable<string> = null
  // 供货-交货地点 名称
  domesticFreightLandName: Nullable<string> = null
  // 供货-国内运输模式。car-汽车
  dmTransportTypeId: Nullable<string> = null
  // 设备特价申请单其他费用信息物料信息明细
  lines: SpecialApplyLogisticsExpensesMatLine[] = []
}